import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Animated,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";
import { Link, useRouter } from "expo-router";
import customFetchWithToken from "@/app/utils/axiosInterceptor";
import Toast, { BaseToast } from "react-native-toast-message";
import * as SecureStore from "expo-secure-store";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Notifications from "./Notifications";
import { showToastSuccess, showToastError, toastConfig } from "../hooks/toast";

const menuItems = [
  { icon: "grid-outline", label: "Dashboard", route: "/dashboard" },
  { icon: "add-circle-outline", label: "Add Listing", route: "/addlisting" },
  { icon: "search-outline", label: "Search Ads", route: "/searchads" },
  { icon: "cash-outline", label: "Remflow Funds", route: "/remflowfunds" },
  { icon: "hammer-outline", label: "Disputes", route: "/disputes" },
  { icon: "time-outline", label: "History", route: "/history" },
  {
    icon: "bookmark-outline",
    label: "Saved Accounts",
    route: "/accounts",
  },
  { icon: "list-outline", label: "My Listings", route: "/mylisting" },
  { icon: "person-outline", label: "Profile", route: "/profile" },
  { icon: "help-circle-outline", label: "Help", route: "/help" },
  // { icon: "person-outline", label: "Trade", route: "/trade" },
  { icon: "log-out-outline", label: "Logout", route: "" },
];

export default function Layout({ children }: { children: React.ReactNode }) {
  const toastConfig = {
    success: (props: any) => (
      <BaseToast
        {...props}
        style={{ borderLeftColor: "green", width: "90%" }} // Adjust width to 100%
        contentContainerStyle={{ paddingHorizontal: 5 }}
        text1Style={{
          fontSize: 14,
          fontWeight: "bold",
          marginLeft: 20,
        }}
        text2Style={{
          fontSize: 29,
        }}
      />
    ),
    // You can add similar customizations for 'error' and 'info' types if needed
  };

  const showToast = (message: string) => {
    Toast.show({
      type: "success", // can also be 'error' or 'info'
      text1: message,
      // text2: "This is a toast message 👋",
      position: "top", // or 'bottom'
      visibilityTime: 4000, // duration in milliseconds
    });
  };
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isNotiOpen, setIsNotiOpen] = useState(false);
  const [notificationCount, setNotificationCount] = useState(0);
  const router = useRouter();
  const slideAnim = useRef(new Animated.Value(-320)).current; // Start off-screen

  // Animation values for hamburger to cross transition
  const topLineRotation = useRef(new Animated.Value(0)).current;
  const middleLineOpacity = useRef(new Animated.Value(1)).current;
  const bottomLineRotation = useRef(new Animated.Value(0)).current;
  const topLineTranslateY = useRef(new Animated.Value(0)).current;
  const bottomLineTranslateY = useRef(new Animated.Value(0)).current;

  const animateHamburgerToCross = () => {
    Animated.parallel([
      // Rotate top line to 45 degrees and move down
      Animated.timing(topLineRotation, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(topLineTranslateY, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      // Fade out middle line
      Animated.timing(middleLineOpacity, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      // Rotate bottom line to -45 degrees and move up
      Animated.timing(bottomLineRotation, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(bottomLineTranslateY, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const animateCrossToHamburger = () => {
    Animated.parallel([
      // Reset top line rotation and position
      Animated.timing(topLineRotation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(topLineTranslateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      // Fade in middle line
      Animated.timing(middleLineOpacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      // Reset bottom line rotation and position
      Animated.timing(bottomLineRotation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(bottomLineTranslateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const toggleMenu = () => {
    if (isMenuOpen) {
      // Close menu and animate cross back to hamburger
      animateCrossToHamburger();
      Animated.timing(slideAnim, {
        toValue: -320,
        duration: 300,
        useNativeDriver: true,
      }).start(() => setIsMenuOpen(false));
    } else {
      // Open menu and animate hamburger to cross
      setIsMenuOpen(true);
      animateHamburgerToCross();
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  };

  const closeNotificationModalFunc = () => {
    setIsNotiOpen(false);
  };

  const clearCacheAndLogout = async () => {
    try {
      // Clear all SecureStore items
      const itemsToDelete = [
        "user",
        "userID",
        "userName",
        "lastname",
        "refreshToken",
        "userEmail",
        "chatToken",
      ];

      for (const item of itemsToDelete) {
        await SecureStore.deleteItemAsync(item);
      }
      console.log("SecureStore cleared.");

      // Clear AsyncStorage
      await AsyncStorage.clear();
      console.log("AsyncStorage cleared.");

      // Remove the router.replace("/") from here as it's already in handleLogout
      console.log("User logged out and cache cleared.");
    } catch (error) {
      console.error("Error clearing cache and logging out:", error);
    }
  };

  const handleLogout = async () => {
    try {
      const res = await customFetchWithToken.post("/destroy-user-token/");
      console.log("res", res.status);
      if (res.status == 200) {
        showToast("User Logged Out Successfully");
        await clearCacheAndLogout();
        setTimeout(() => {
          router.replace("/");
        }, 3000);
      }
    } catch (error) {
      console.error(error);
    }
    // After logout, navigate to login screen
    // router.replace("/login");
  };

  const getAllNotificationsHandler = async () => {
    try {
      const res = await customFetchWithToken("/get-notification/");
      setNotificationCount(res.data.count);
    } catch (error) {
      console.log(error);
    }
  };

  const notificationCountHandler = (data: number) => {
    setNotificationCount(data); // Still valid
  };

  useEffect(() => {
    getAllNotificationsHandler();
  }, []);

  return (
    <SafeAreaView className="flex-1 bg-gray-100">
      <View className="flex-auto">
        {/* Top Bar - Custom gradient: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #1e1b4b 100%) */}
        <LinearGradient
          colors={['#1e3a8a', '#3730a3', '#1e1b4b']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          locations={[0, 0.5, 1]}
          className="h-16 flex-row items-center justify-between px-4"
          style={{
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.3,
            shadowRadius: 8,
            elevation: 8,
          }}
        >
          <TouchableOpacity
            onPress={toggleMenu}
            className="p-2 rounded-xl active:bg-white/10 transition-colors duration-200"
          >
            {/* Animated Hamburger/Cross Icon */}
            <View className="w-6 h-6 items-center justify-center">
              {/* Top Line */}
              <Animated.View
                className="absolute w-5 h-0.5 bg-white rounded-full"
                style={{
                  transform: [
                    {
                      translateY: topLineTranslateY.interpolate({
                        inputRange: [0, 1],
                        outputRange: [-6, 0],
                      }),
                    },
                    {
                      rotate: topLineRotation.interpolate({
                        inputRange: [0, 1],
                        outputRange: ['0deg', '45deg'],
                      }),
                    },
                  ],
                }}
              />

              {/* Middle Line */}
              <Animated.View
                className="absolute w-5 h-0.5 bg-white rounded-full"
                style={{
                  opacity: middleLineOpacity,
                }}
              />

              {/* Bottom Line */}
              <Animated.View
                className="absolute w-5 h-0.5 bg-white rounded-full"
                style={{
                  transform: [
                    {
                      translateY: bottomLineTranslateY.interpolate({
                        inputRange: [0, 1],
                        outputRange: [6, 0],
                      }),
                    },
                    {
                      rotate: bottomLineRotation.interpolate({
                        inputRange: [0, 1],
                        outputRange: ['0deg', '-45deg'],
                      }),
                    },
                  ],
                }}
              />
            </View>
          </TouchableOpacity>

          <View className="flex-1 items-center">
            <Text className="text-white text-lg font-bold tracking-tight">
              RemFlow
            </Text>
          </View>

          <TouchableOpacity
            onPress={() => setIsNotiOpen(!isNotiOpen)}
            className="relative p-1 rounded-xl active:bg-white/10 transition-colors duration-200"
          >
            <View className="w-8 h-8 bg-white/20 rounded-full items-center justify-center backdrop-blur-sm border border-white/30">
              <Ionicons name="notifications" size={20} color="white" />
              {notificationCount > 0 && (
                <View className="absolute -top-1 -right-1 bg-red-500 rounded-full w-4 h-4 items-center justify-center shadow-lg">
                  <Text className="text-white text-xs font-bold">
                    {notificationCount > 99 ? "99+" : notificationCount}
                  </Text>
                </View>
              )}
            </View>
          </TouchableOpacity>
          {isNotiOpen && (
            <Notifications
              isVisible={isNotiOpen}
              onClose={() => setIsNotiOpen(false)}
              handleNotiCount={notificationCountHandler}
            />
          )}
          {/* <TouchableOpacity onPress={() => router.push("/profile")}>
            <View className="w-8 h-8 bg-gray-300 rounded-full items-center justify-center">
              <Ionicons name="person" size={20} color="white" />
            </View>
          </TouchableOpacity> */}
        </LinearGradient>

        {/* Sliding Menu - Enhanced with backdrop blur and gradient */}
        {isMenuOpen && (
          <Animated.View
            className="absolute left-0 top-16 bottom-0 w-80 max-w-[85%] bg-white z-10 shadow-2xl border-r border-indigo-100"
            style={{
              transform: [{ translateX: slideAnim }],
            }}
          >
            <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
              {/* Welcome Section */}
              <View className="bg-gradient-to-r from-indigo-50 to-blue-50 p-4 border-b border-indigo-100">
                <Text className="text-lg font-bold text-gray-800 mb-1">Welcome Back!</Text>
                <Text className="text-sm text-gray-600">Manage your trades and transactions</Text>
              </View>

              {/* Menu Items */}
              <View className="py-1">
                {menuItems.map((item, index) => {
                  const isLastItem = index === menuItems.length - 1;

                  if (item.label === "Logout") {
                    return (
                      <View key={index}>
                        <TouchableOpacity
                          className="flex-row items-center py-3 px-4 mx-2 active:bg-red-50 transition-colors duration-200"
                          onPress={() => {
                            toggleMenu();
                            handleLogout();
                          }}
                        >
                          <View className="w-8 h-8 bg-red-100 rounded-lg items-center justify-center mr-3">
                            <Ionicons
                              name={item.icon as any}
                              size={18}
                              color="#dc2626"
                            />
                          </View>
                          <Text className="text-red-600 font-medium text-base">
                            {item.label}
                          </Text>
                        </TouchableOpacity>
                      </View>
                    );
                  }

                  return (
                    <View key={index}>
                      <Link href={item.route as any} asChild>
                        <TouchableOpacity
                          className="flex-row items-center py-3 px-4 mx-2 active:bg-indigo-50 transition-colors duration-200"
                          onPress={() => {
                            toggleMenu();
                          }}
                        >
                          <View className="w-8 h-8 bg-indigo-100 rounded-lg items-center justify-center mr-3">
                            <Ionicons
                              name={item.icon as any}
                              size={18}
                              color="#4f46e5"
                            />
                          </View>
                          <Text className="text-gray-800 font-medium text-base">
                            {item.label}
                          </Text>
                        </TouchableOpacity>
                      </Link>
                      {!isLastItem && (
                        <View className="mx-4 border-b border-gray-200" />
                      )}
                    </View>
                  );
                })}
              </View>
            </ScrollView>
          </Animated.View>
        )}

        {/* Main Content */}
        <View className="flex-1 bg-white">{children}</View>

        {/* Overlay to close menu when clicked outside */}
        {isMenuOpen && (
          <TouchableOpacity
            className="absolute inset-0 bg-black bg-opacity-50 z-0"
            onPress={toggleMenu}
          />
        )}
      </View>
      <Toast config={toastConfig} />
    </SafeAreaView>
  );
}
